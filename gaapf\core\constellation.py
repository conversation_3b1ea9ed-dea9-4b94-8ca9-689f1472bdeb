"""
Constellation Manager for GAAPF.

This module implements the Adaptive Learning Constellation architecture,
which dynamically forms specialized agent teams based on user learning patterns
and contextual requirements.
"""

from enum import Enum
from typing import Dict, List, Optional, Any, Tuple
import asyncio
import json
import os
from pathlib import Path

from langchain_core.language_models.base import BaseLanguageModel
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage

# Import from vinagent
from vinagent.agent import Agent
from vinagent.graph.function_graph import FunctionStateGraph

# Import GAAPF agents
from gaapf.agents.base_agent import BaseGAAPFAgent

# Import framework configs
from gaapf.config.framework_configs import SupportedFrameworks, get_framework_config, DEFAULT_FRAMEWORK_CONFIGS


class ConstellationType(Enum):
    """Types of agent constellations available in the system."""
    
    KNOWLEDGE_INTENSIVE = "knowledge_intensive"
    HANDS_ON_FOCUSED = "hands_on_focused"
    THEORY_PRACTICE_BALANCED = "theory_practice_balanced"
    BASIC_LEARNING = "basic_learning"
    GUIDED_LEARNING = "guided_learning"


class ConstellationConfig:
    """Configuration for a specific constellation type."""
    
    def __init__(
        self,
        primary_agents: List[str],
        support_agents: List[str],
        description: str,
        best_for: List[str]
    ):
        self.primary_agents = primary_agents
        self.support_agents = support_agents
        self.description = description
        self.best_for = best_for


class ConstellationManager:
    """
    Manages the formation and execution of agent constellations.
    
    The ConstellationManager is responsible for:
    1. Selecting the optimal constellation type based on user profile and context
    2. Creating and configuring agent constellations
    3. Managing agent handoffs and communication
    4. Tracking constellation effectiveness
    """
    
    def __init__(self, config_path: Path = None):
        """
        Initialize the ConstellationManager.
        
        Args:
            config_path: Path to constellation configurations
        """
        self.constellations = {}
        self.constellation_configs = self._load_constellation_configs(config_path)
        self.framework_configs = DEFAULT_FRAMEWORK_CONFIGS
        
    def _load_constellation_configs(self, config_path: Optional[Path] = None) -> Dict[ConstellationType, ConstellationConfig]:
        """
        Load constellation configurations from file or use defaults.
        
        Args:
            config_path: Path to constellation configurations JSON file
            
        Returns:
            Dictionary mapping constellation types to their configurations
        """
        # Default configurations if no file is provided
        default_configs = {
            ConstellationType.KNOWLEDGE_INTENSIVE: ConstellationConfig(
                primary_agents=["instructor", "documentation_expert", "knowledge_synthesizer"],
                support_agents=["research_assistant", "progress_tracker"],
                description="Focus on theoretical understanding and knowledge acquisition",
                best_for=["Conceptual learning", "Theoretical foundations"]
            ),
            ConstellationType.HANDS_ON_FOCUSED: ConstellationConfig(
                primary_agents=["code_assistant", "practice_facilitator", "project_guide"],
                support_agents=["troubleshooter", "mentor"],
                description="Focus on practical implementation and coding skills",
                best_for=["Learning by doing", "Practical skills"]
            ),
            ConstellationType.THEORY_PRACTICE_BALANCED: ConstellationConfig(
                primary_agents=["instructor", "code_assistant", "practice_facilitator"],
                support_agents=["documentation_expert", "mentor"],
                description="Balanced approach between theory and practice",
                best_for=["Comprehensive understanding", "Balanced learning"]
            ),
            ConstellationType.BASIC_LEARNING: ConstellationConfig(
                primary_agents=["instructor", "code_assistant"],
                support_agents=["mentor", "practice_facilitator"],
                description="Gentle introduction for beginners",
                best_for=["Beginners", "Foundational learning"]
            ),
            ConstellationType.GUIDED_LEARNING: ConstellationConfig(
                primary_agents=["instructor", "mentor"],
                support_agents=["code_assistant", "practice_facilitator"],
                description="Structured guidance with personalized support",
                best_for=["Users needing extra support", "Structured learning paths"]
            )
        }
        
        # If config file exists, load from it (would override defaults)
        if config_path and config_path.exists():
            try:
                with open(config_path, "r") as f:
                    config_data = json.load(f)
                    
                # Process the loaded configurations
                # Implementation would parse the JSON into ConstellationConfig objects
                pass
            except Exception as e:
                print(f"Error loading constellation configs: {e}")
                
        return default_configs
    
    async def create_constellation(
        self,
        constellation_type: ConstellationType,
        user_profile: Dict,
        framework: str,
        module_id: str,
        session_id: str,
        llm: BaseLanguageModel
    ) -> Dict[str, BaseGAAPFAgent]:
        """
        Create a new constellation based on type and user profile.
        
        Args:
            constellation_type: Type of constellation to create
            user_profile: User profile information
            framework: Target framework (e.g., "langchain", "langgraph")
            module_id: Current learning module
            session_id: Unique session identifier
            llm: Language model to use for agents
            
        Returns:
            Dictionary of configured agents in the constellation
        """
        config = self.constellation_configs[constellation_type]
        agents = {}
        
        # Create primary agents
        for agent_type in config.primary_agents:
            agent = await self._create_agent(
                agent_type=agent_type,
                user_profile=user_profile,
                framework=framework,
                module_id=module_id,
                session_id=session_id,
                llm=llm,
                is_primary=True
            )
            agents[agent_type] = agent
            
        # Create support agents
        for agent_type in config.support_agents:
            agent = await self._create_agent(
                agent_type=agent_type,
                user_profile=user_profile,
                framework=framework,
                module_id=module_id,
                session_id=session_id,
                llm=llm,
                is_primary=False
            )
            agents[agent_type] = agent
            
        # Store the constellation
        self.constellations[session_id] = {
            "type": constellation_type,
            "agents": agents,
            "framework": framework,
            "module_id": module_id,
            "user_profile": user_profile
        }
        
        return agents
    
    async def _create_agent(
        self,
        agent_type: str,
        user_profile: Dict,
        framework: str,
        module_id: str,
        session_id: str,
        llm: BaseLanguageModel,
        is_primary: bool
    ) -> BaseGAAPFAgent:
        """
        Create and configure an individual agent.
        
        Args:
            agent_type: Type of agent to create
            user_profile: User profile information
            framework: Target framework
            module_id: Current learning module
            session_id: Unique session identifier
            llm: Language model to use
            is_primary: Whether this is a primary agent
            
        Returns:
            Configured BaseGAAPFAgent instance
        """
        # This would be implemented to create the appropriate agent type
        # For now, we'll return a placeholder
        from gaapf.agents.base_agent import create_agent
        
        return await create_agent(
            agent_type=agent_type,
            user_profile=user_profile,
            framework=framework,
            module_id=module_id,
            session_id=session_id,
            llm=llm,
            is_primary=is_primary
        )
    
    async def run_session(
        self,
        session_id: str,
        user_message: str,
        user_profile: Dict,
        framework: str,
        module_id: str
    ) -> Dict:
        """
        Run a learning session with the specified constellation.
        
        Args:
            session_id: Unique session identifier
            user_message: User's message/question
            user_profile: User profile information
            framework: Target framework
            module_id: Current learning module
            
        Returns:
            Session result including agent responses
        """
        if session_id not in self.constellations:
            raise ValueError(f"No constellation found for session {session_id}")
            
        constellation = self.constellations[session_id]
        
        # Determine the initial agent based on message content
        initial_agent = await self._select_initial_agent(
            constellation=constellation,
            user_message=user_message
        )
        
        # Process the message through the selected agent
        response = await initial_agent.ainvoke(
            query=user_message,
            user_id=user_profile.get("user_id", "unknown_user")
        )
        
        # Check if handoff is needed
        handoff_agent = await self._check_handoff(
            constellation=constellation,
            current_agent=initial_agent,
            response=response,
            user_message=user_message
        )
        
        # If handoff is needed, process through the next agent
        if handoff_agent and handoff_agent != initial_agent:
            handoff_response = await handoff_agent.ainvoke(
                query=f"Previous agent response: {response.content}\nUser question: {user_message}",
                user_id=user_profile.get("user_id", "unknown_user")
            )
            response = handoff_response
        
        return {
            "session_id": session_id,
            "response": response,
            "agent_path": [initial_agent.__class__.__name__] + 
                         ([handoff_agent.__class__.__name__] if handoff_agent and handoff_agent != initial_agent else [])
        }
    
    async def _select_initial_agent(self, constellation: Dict, user_message: str):
        """
        Select the initial agent to handle the user message.

        Args:
            constellation: Current constellation configuration
            user_message: User's message/question

        Returns:
            Selected agent instance
        """
        agents = constellation["agents"]

        # Calculate confidence scores for all agents
        agent_scores = {}
        for agent_name, agent in agents.items():
            confidence = agent.get_confidence_score(user_message)
            agent_scores[agent_name] = confidence

        # Select the agent with the highest confidence
        best_agent_name = max(agent_scores, key=agent_scores.get)
        return agents[best_agent_name]

    async def _check_handoff(
        self,
        constellation: Dict,
        current_agent,
        response: Any,
        user_message: str
    ):
        """
        Check if a handoff to another agent is needed.

        Args:
            constellation: Current constellation configuration
            current_agent: Current agent handling the request
            response: Current agent's response
            user_message: Original user message

        Returns:
            Next agent if handoff is needed, None otherwise
        """
        # Get handoff analysis from the current agent
        if hasattr(response, 'get') and 'handoff_analysis' in response:
            handoff_analysis = response['handoff_analysis']
        else:
            # If response doesn't have handoff analysis, analyze the content
            content = response.content if hasattr(response, 'content') else str(response)
            handoff_analysis = current_agent._analyze_content_for_handoff(content, user_message)

        # Check if handoff is needed
        if handoff_analysis.get("needs_handoff", False):
            suggested_agent_type = handoff_analysis.get("suggested_agent")
            confidence = handoff_analysis.get("confidence", 0.0)

            # Only handoff if confidence is above threshold
            if confidence >= 0.6 and suggested_agent_type:
                agents = constellation["agents"]
                if suggested_agent_type in agents:
                    return agents[suggested_agent_type]

        return None
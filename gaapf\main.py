"""
Main entry point for GAAPF.

This module provides the main entry point for the GAAPF system,
handling command-line arguments and launching the appropriate interface.
"""

import argparse
import asyncio
import logging
import sys
import subprocess
import os
from pathlib import Path

# Configure logging with environment variable support
log_level = os.getenv("LOG_LEVEL", "INFO").upper()
logging.basicConfig(
    level=getattr(logging, log_level, logging.INFO),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def main():
    """Main entry point for GAAPF."""
    parser = argparse.ArgumentParser(
        description="GAAPF - Guidance AI Agent for Python Framework",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python -m gaapf                          # Launch CLI interface
  python -m gaapf --interface cli          # Launch CLI interface (explicit)
  python -m gaapf --help                   # Show this help message

The CLI interface provides the full GAAPF experience with real LLM integration.
        """
    )
    parser.add_argument(
        "--interface",
        choices=["cli"],
        default="cli",
        help="Interface to use (CLI only)"
    )
    parser.add_argument(
        "--data-path",
        type=Path,
        default=Path("data"),
        help="Path to data directory (default: data)"
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug logging"
    )


    args = parser.parse_args()

    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)

    # Ensure data directory exists
    args.data_path.mkdir(exist_ok=True, parents=True)

    logger.info(f"Starting GAAPF with {args.interface} interface")
    logger.info(f"Data path: {args.data_path}")

    try:
        if args.interface == "cli":
            # Launch CLI interface
            from gaapf.interfaces.cli.app import GAAPFCli
            cli_app = GAAPFCli(data_path=args.data_path)
            await cli_app.run()
        else:
            logger.error(f"Unknown interface: {args.interface}")
            sys.exit(1)

    except KeyboardInterrupt:
        logger.info("Shutting down GAAPF...")
    except Exception as e:
        logger.error(f"Error running GAAPF: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        sys.exit(1)


def main_cli():
    """CLI entry point for setuptools."""
    asyncio.run(main())


if __name__ == "__main__":
    asyncio.run(main())